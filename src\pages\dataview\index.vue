<template>
  <div class="data-view-content">
    <div class="upper-half">
      <!-- 概况 -->
      <Overview
        :overviewInfo="overviewInfo"
        :dataSource="dataSource"
        :loading="overviewLoading"
      />
      <!-- 风险sql -->
      <Topsql
        :ruleList="ruleList"
        :passRate="passRate"
        :sqlParsingRate="sqlParsingRate"
        :loading="topsqlLoading"
      />
      <!-- 数据库对象监控 -->
      <DatabaseMonitor :loading="monitorLoading" :monitorInfo="monitorInfo" />
      <!-- 代办事项 -->
      <Todo
        :skipMap="skipMap"
        :taskData="taskData"
        :loading="todoLoading"
        :selected-project="selectedProject"
        :selected-data-source="selectedDataSource"
        @refresh="refresh"
        @data-source-change="handleDataSourceChange"
        @back-to-parent="handleBackToParent"
      />
    </div>
    <div class="bottom-half">
      <!-- 代码审核 -->
      <CodeReview
        :riskMap="riskMap"
        :riskTag="riskTag"
        :riskClass="riskClass"
        :codeReviewData="codeReviewData"
        :barData="barData"
        :timeRange="timeRange"
        :loading="codeReviewLoading"
        :projectType="projectType"
        :projectName="projectName"
        :codeReviewOption="codeReviewOption"
      />
      <!-- 数据审核 -->
      <DatabaseReview
        :riskMap="riskMap"
        :riskTag="riskTag"
        :riskClass="riskClass"
        :databaseAuditInfo="databaseAuditInfo"
        :pieOption="pieOption"
        :loading="databaseReviewLoading"
      />
      <!-- 版本信息 -->
      <Version
        :updataInfo="updataInfo"
        :feedList="feedList"
        :version="version"
        :loading="versionLoading"
        :feedLoading="feedLoading"
      />
    </div>
  </div>
</template>

<script>
import Overview from './Overview';
import Topsql from './Topsql';
import DatabaseMonitor from './DatabaseMonitor';
import Todo from './Todo';
import CodeReview from './CodeReview';
import DatabaseReview from './DatabaseReview';
import Version from './Version';
import config from './config';
import bodyMinWidth from '@/mixins/bodyMinWidth';

import {
  getTask,
  getReportIndex,
  getReportVersion,
  getFeedBack,
  getReportCodeReview,
  getReportTopSql,
  getReportDatabaseAudit,
  getDatabaseMonitorData
} from '@/api/dataview';
export default {
  mixins: [bodyMinWidth(1440)],
  components: {
    Overview,
    Topsql,
    DatabaseMonitor,
    CodeReview,
    Todo,
    DatabaseReview,
    Version
  },
  props: {
    selectedProject: {
      type: [String, Number],
      default: null
    },
    selectedDataSource: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    this.config = config(this);
    let role = this.$store.state.account.user.role;
    return {
      role,
      overviewLoading: false,
      topsqlLoading: false,
      monitorLoading: false,
      todoLoading: false,
      codeReviewLoading: false,
      databaseReviewLoading: false,
      versionLoading: false,
      feedLoading: false,
      dataSource: [],
      pieOption: this.config.pieOption(),
      codeReviewOption: this.config.codeReviewOption(),
      overviewInfo: [],
      passRate: 0,
      sqlParsingRate: 0,
      ruleList: [],
      feedList: [],
      updataInfo: [],
      codeReviewData: {},
      databaseAuditInfo: {},
      monitorInfo: [],
      taskData: {},
      barData: [],
      riskMap: {
        high_risk: '高风险',
        lower_risk: '低风险',
        no_risk: '无风险',
        modifying_sql: '整改中',
        whitelist_sql: '白名单',
        task_total: '发起任务数',
        sql_count: 'SQL总数',
        data_source_monitoring_number: '监控数据源个数'
      },
      riskTag: {
        high_risk: '高',
        lower_risk: '低',
        no_risk: '无',
        modifying_sql: '整',
        whitelist_sql: '白'
      },
      riskClass: {
        high_risk: 'high-risk',
        lower_risk: 'lower-risk',
        no_risk: 'no-risk',
        modifying_sql: 'modifying',
        whitelist_sql: 'white-list'
      },
      skipMap: [
        {
          name: '项目审核',
          icon: 'project',
          route: 'project-review'
        },
        {
          name: '存储过程审核',
          icon: 'lu-icon-storage',
          route: 'procedure-review'
        },
        {
          name: '数据库对象监控',
          icon: 'video-camera',
          route: 'monitoring'
        },
        {
          name: '快速审核',
          icon: 'lu-icon-qreview',
          route: 'quickAudit'
        },
        {
          name: 'TOP SQL',
          icon: 'lu-icon-topsql',
          route: 'top-sql'
        },
        {
          name: 'RealTime SQL',
          icon: 'lu-icon-realtimesql',
          route: 'real-time'
        },
        {
          name: '文件审核',
          icon: 'file-search',
          route: 'file-review'
        },
        {
          name: '项目',
          icon: 'project',
          route: 'project-config',
          visible: !['developer', 'leader'].includes(role)
        },
        {
          name: '数据源',
          icon: 'lu-icon-database',
          route: 'data-source',
          visible: !['developer', 'leader'].includes(role)
        }
        // {
        //   name: '导出报表',
        //   icon: 'file-text',
        //   route: 'export'
        // }
      ].filter(item => item.visible !== false),
      version: '',
      projectGroupId: undefined,
      projectId: undefined,
      dataSourceId: undefined,
      treeId: undefined,
      timeType: undefined,
      timePicker: undefined,
      timeRange: [],
      projectType: 'group',
      projectName: ''
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  beforeDestroy() {},
  methods: {
    init() {
      this.getIndex();
      this.getTopSql();
      this.getVersion();
      this.getFeed();
      this.getCodeReview();
      this.getDatabaseAudit();
      this.getTaskFn();
      this.getDatabaseMonitor();
    },
    getTaskFn() {
      this.todoLoading = true;
      getTask()
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.taskData = _.get(res, 'data.data');
            this.todoLoading = false;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          console.error(e);
          this.$hideLoading({
            method: 'error',
            tips: e.response.data.message || '请求失败'
          });
        });
    },
    getIndex() {
      const params = {
        tree_id: this.treeId,
        data_source_id: this.dataSourceId,
        type: this.timeType,
        date: this.timePicker
      };
      this.overviewLoading = true;
      getReportIndex(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.overviewLoading = false;
            const resData = _.get(res, 'data.data');
            this.overviewInfo = [
              {
                label: '项目数量',
                value: resData.project_count,
                icon: 'project'
              },
              {
                label: '数据源',
                value: resData.data_source_count,
                icon: 'lu-icon-database'
              },
              { label: '用户数量', value: resData.user_count, icon: 'user' }
            ];
            const list = resData.data_source_list || [];
            this.dataSource = list;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getVersion() {
      this.versionLoading = true;
      getReportVersion()
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.versionLoading = false;
            const resData = _.get(res, 'data.data');
            this.updataInfo = resData.data;
            this.version = resData.version;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getFeed() {
      this.feedLoading = true;
      getFeedBack()
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data');
            this.feedList = resData.results;
            this.feedLoading = false;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getCodeReview() {
      const params = {
        tree_id: this.treeId,
        data_source_id: this.dataSourceId,
        type: this.timeType,
        date: this.timePicker
      };
      this.codeReviewLoading = true;
      getReportCodeReview(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            this.codeReviewLoading = false;
            const resData = _.get(res, 'data.data');
            const chart = resData.chart;
            this.projectType = resData.form;
            this.timeRange = resData.time_range || [];
            this.codeReviewData = {
              task_total: resData.task_total,
              high_risk: resData.high_risk,
              lower_risk: resData.lower_risk,
              no_risk: resData.no_risk,
              modifying_sql: resData.modifying_sql_total,
              whitelist_sql: resData.whitelist_sql_total
            };
            if (this.projectType == 'group') {
              // const color = {
              //   high_risk: { color: '#FF7875' },
              //   lower_risk: { color: '#FAAD14' },
              //   modifying_sql: { color: '#4096FF' },
              //   whitelist_sql: { color: '#52C41A' }
              // };
              // let barData = [];
              // for (let key in chart) {
              //   const arr = chart[key];
              //   const _arr = arr.map(item => {
              //     return {
              //       ...item,
              //       itemStyle: color[item.name]
              //     };
              //   });
              //   barData.push({
              //     projectName: key,
              //     options: _arr
              //   });
              // }
              this.barData = chart || [];
            }

            if (this.projectType == 'project') {
              let obj = {
                high_risk: '高风险',
                lower_risk: '低风险',
                no_risk: '无风险',
                error_risk: '异常'
              };
              // let rate = 0;
              let count = chart.count;
              this.projectName = chart.project_name;
              const arr = chart.risk_value;
              const _arr = arr.map(item => {
                // if (item.name == 'high_risk') {
                //   rate = Number((item.rate * 100).toFixed(2));
                // }
                item.rate = (item.rate * 100).toFixed(2) + '%';
                item.name = obj[item.name];
                return item;
              });
              this.$set(
                this,
                'codeReviewOption',
                this.config.codeReviewOption(_arr, count)
              );
            }
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getTopSql() {
      const params = {
        tree_id: this.treeId,
        data_source_id: this.dataSourceId,
        type: this.timeType,
        date: this.timePicker
      };
      this.topsqlLoading = true;
      getReportTopSql(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data');
            this.passRate = resData.pass_rate || 0;
            this.sqlParsingRate = resData.sql_parsing_rate || 0;
            this.ruleList = resData.rule_list || [];
            this.topsqlLoading = false;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getDatabaseAudit() {
      const params = {
        tree_id: this.treeId,
        data_source_id: this.dataSourceId,
        type: this.timeType,
        date: this.timePicker
      };
      this.databaseReviewLoading = true;
      getReportDatabaseAudit(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data');
            const list = resData.list;
            // 自定义字段在obj里面的顺序
            this.databaseAuditInfo = {
              data_source_monitoring_number:
                list.data_source_monitoring_number || 0,
              sql_count: list.sql_count || 0,
              high_risk: list.high_risk || 0,
              lower_risk: list.lower_risk || 0,
              no_risk: list.no_risk || 0
            };
            const chartData = resData.chart || [];
            let obj = {
              high_risk: '高风险',
              lower_risk: '低风险',
              no_risk: '无风险'
            };
            let rate = 0;
            const arr = chartData.map(item => {
              if (item.name == 'high_risk') {
                rate = Number((item.rate * 100).toFixed(2));
              }
              item.rate = (item.rate * 100).toFixed(2) + '%';
              item.name = obj[item.name];
              return item;
            });
            this.$set(this, 'pieOption', this.config.pieOption(arr, rate));
            this.databaseReviewLoading = false;
          } else {
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    getDatabaseMonitor() {
      const params = {
        tree_id: this.treeId,
        data_source_id: this.dataSourceId,
        type: this.timeType,
        date: this.timePicker
      };
      this.monitorLoading = true;
      getDatabaseMonitorData(params)
        .then(res => {
          if (_.get(res, 'data.code') == 0) {
            const resData = _.get(res, 'data.data');
            this.monitorInfo = [
              {
                label: '数据库大小(TB)',
                icon: 'lu-icon-database',
                value: resData.schemaSize,
                chartType: 'line',
                chartData: resData.seriesSchemaSize
              },
              {
                label: '表数量',
                icon: 'table',
                value: resData.tableCount,
                chartType: 'bar',
                chartData: resData.seriesTableCount
              },
              {
                label: '表大小(TB)',
                icon: 'table',
                value: resData.tableSize,
                chartType: 'line',
                chartData: resData.seriesTableSize
              },
              {
                label: '索引数量',
                icon: 'lu-icon-index',
                value: resData.indexCount,
                chartType: 'bar',
                chartData: resData.seriesIndexCount
              },
              {
                label: '索引大小(TB)',
                icon: 'lu-icon-index',
                value: resData.indexSize,
                chartType: 'line',
                chartData: resData.seriesIndexSize
              }
            ];
            this.monitorLoading = false;
          } else {
            this.monitorLoading = false;
            this.$hideLoading({
              method: 'error',
              tips: _.get(res, 'data.message')
            });
          }
        })
        .catch(e => {
          this.monitorLoading = false;
          this.$hideLoading({
            method: 'error',
            tips: _.get(e || {}, 'response.data.message') || '请求失败'
          });
        });
    },
    refresh(treeId, dataSourceId, timeType, timePicker) {
      this.treeId = treeId;
      this.dataSourceId = dataSourceId;
      this.timeType = timeType;
      this.timePicker = timePicker;
      this.init();
    },

    // 处理数据源变化
    handleDataSourceChange(dataSource) {
      this.$emit('data-source-change', dataSource);
    },

    // 处理返回父级卡片
    handleBackToParent() {
      this.$emit('back-to-parent');
    }
  }
};
</script>

<style lang="less" scoped>
.data-view-content {
  margin: -24px 0 0 0;
  .upper-half {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }
  .bottom-half {
    display: flex;
    justify-content: space-between;
  }
}
@media screen and(max-width: 1700px) {
  .data-view-content {
    /deep/.upper-half {
      justify-content: flex-start;
      flex-wrap: wrap;
      margin-bottom: 0;
      .quater-block {
        &.small-size {
          width: calc(25% - 12px);
          margin-bottom: 16px;
        }
        &.overview-block {
          margin-right: 12px;
        }
        &.topsql-block {
          margin-right: 12px;
          .data-overview {
            margin-bottom: 20px;
          }
        }
        &.monitor-block {
          width: calc(25% - 0px);
          margin-right: 12px;
        }
        &.todo-block {
          width: calc(25% - 12px);
          margin-right: 0;
          .to-do-list {
            .events {
              > div {
                > div {
                  span:last-child {
                    width: 60px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: center;
                  }
                }
              }
              &.developer-events {
                > div {
                  > div {
                    span:last-child {
                      width: 100px;
                    }
                  }
                }
              }
            }
            .skip {
              > div {
                span:last-child {
                  width: 56px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  text-align: center;
                }
              }
            }
          }
        }
      }
    }
    .bottom-half {
      /deep/.half-block {
        width: calc(50% - 12px);
        margin-bottom: 0;
        margin-right: 12px;
        .code-review {
          .chart-view-area {
            .header-info {
              span {
                font-size: 12px;
                white-space: nowrap;
              }
            }
          }
          .small-chart-view {
            > div {
              span {
                display: inline-block;
                width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            &.col-7 {
              > div {
                span {
                  width: 64px;
                }
              }
            }
            &.col-8 {
              > div {
                span {
                  width: 40px;
                }
              }
            }
          }
        }
      }
      /deep/.quater-block {
        &.small-size {
          width: calc(25% - 0px);
          margin-right: 12px;
        }
        &.version-block {
          width: calc(25% - 12px);
          margin-right: 0;
        }
      }
    }
  }
}
</style>
